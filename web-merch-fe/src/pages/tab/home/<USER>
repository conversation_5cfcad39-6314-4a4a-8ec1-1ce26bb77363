<template>
  <view class="h-full">
    <IndexMerch v-if="userType === 'merch'" />
    <IndexOrg v-else-if="userType === 'org'" />
  </view>
</template>

<script setup lang="ts">
import IndexMerch from './modules/index-merch.vue';
import IndexOrg from './modules/index-org.vue';
import type { UserState } from '@/store/modules/user/types';
import { useUserStore } from '@/store';
import checkUpdate from '@/utils/check-update';

const userType: UserState['userType'] = useUserStore().userType;

// #ifdef APP-PLUS
onLoad(() => {
  setTimeout(() => {
    checkUpdate.checkApp({}, true);
  }, 3000); // 延迟到3秒，避免与启动页关闭冲突
});
// #endif
</script>
