<template>
  <view class="h-full">
    <IndexMerch v-if="userType === 'merch'" />
    <IndexOrg v-else-if="userType === 'org'" />
  </view>
</template>

<script setup lang="ts">
import IndexMerch from './modules/index-merch.vue';
import IndexOrg from './modules/index-org.vue';
import type { UserState } from '@/store/modules/user/types';
import { useUserStore } from '@/store';
import checkUpdate from '@/utils/check-update';

const userType: UserState['userType'] = useUserStore().userType;

// #ifdef APP-PLUS
onLoad(() => {
  // 延迟检查更新，确保页面完全加载完成
  setTimeout(() => {
    checkUpdate.checkApp({}, true);
  }, 5000); // 延迟到5秒，确保启动页关闭和页面稳定
});
// #endif
</script>
