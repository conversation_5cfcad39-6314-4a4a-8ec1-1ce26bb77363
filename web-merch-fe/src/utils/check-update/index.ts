/**
 * 检查应用更新
 */
import { CommonApi } from '@/api/common';

/**
 * 更新配置参数接口
 */
export interface UpdateConfig {
  /** 更新提示标题 */
  title?: string;
  /** 更新提示内容 */
  content?: string;
  /** 取消按钮文本 */
  canceltext?: string;
  /** 确认按钮文本 */
  oktext?: string;
  /** 进度条背景色 */
  barbackground?: string;
  /** 进度条激活色 */
  barbackgroundactive?: string;
}

/**
 * 版本信息接口
 */
export interface VersionInfo {
  /** 下载链接 */
  downloadUrl?: string;
  /** 版本描述 */
  versionDesc?: string;
  /** 是否强制升级 */
  isForcedUpgrade?: boolean;
}

/**
 * 设备类型枚举
 */
enum DeviceType {
  ANDROID = 1,
  IOS = 2,
}

/**
 * 平台类型
 */
type Platform = 'android' | 'ios';

/**
 * 默认更新配置
 */
const DEFAULT_CONFIG: Required<UpdateConfig> = {
  title: '检测到有新版本', // 去掉中文感叹号
  content: '请升级app到最新版本',
  canceltext: '暂不升级',
  oktext: '立即升级',
  barbackground: 'rgba(50,50,50,0.8)',
  barbackgroundactive: 'rgba(32, 165, 58, 1)',
};

/**
 * AppStore 地址
 */
const APP_STORE_URL = 'https://www.pgyer.com/';

/**
 * 更新检查状态管理
 */
let isCheckingUpdate = false;

/**
 * 获取设备平台信息
 */
function getPlatformInfo(): { platform: Platform; deviceType: DeviceType } {
  const platform = (plus.os.name?.toLowerCase() || 'android') as Platform;
  const deviceType = platform === 'ios' ? DeviceType.IOS : DeviceType.ANDROID;
  return { platform, deviceType };
}

/**
 * 显示最新版本提示
 */
function showLatestVersionToast(): void {
  uni.showToast({
    title: '当前为最新版本',
    icon: 'none',
  });
}

/**
 * 处理iOS更新
 */
function handleIOSUpdate(config: Required<UpdateConfig>, versionInfo: VersionInfo): void {
  showUpdateModal(config, versionInfo, () => {
    plus.runtime.openURL(APP_STORE_URL);
  });
}

/**
 * 检查app更新
 * @param config 更新配置参数
 * @param silence 是否静默检查（不显示"已是最新版本"提示）
 */
async function checkApp(config: UpdateConfig = {}, silence = false): Promise<void> {
  // 防止重复检查
  if (isCheckingUpdate) {
    console.log('正在检查更新中，跳过重复请求');
    return Promise.resolve();
  }

  const updateConfig = { ...DEFAULT_CONFIG, ...config };
  isCheckingUpdate = true;

  return new Promise((resolve) => {
    plus.runtime.getProperty(plus.runtime.appid as any, async (widgetInfo) => {
      try {
        const { platform, deviceType } = getPlatformInfo();
        const versionCode = widgetInfo.version;
        const marketSource = 'public';

        // 检测APP版本是否有更新
        const result = await CommonApi.checkAppVersion({
          deviceType,
          versionCode,
          marketSource,
        });

        const versionInfo: VersionInfo | null = result?.latestAppVersionRecord || null;

        // 当前已是最新版本
        if (!versionInfo) {
          if (!silence) {
            showLatestVersionToast();
          }
          isCheckingUpdate = false;
          resolve();
          return;
        }

        // 处理更新逻辑
        if (platform === 'android') {
          handleAndroidUpdate(updateConfig, versionInfo);
        }
        else if (platform === 'ios') {
          handleIOSUpdate(updateConfig, versionInfo);
        }

        isCheckingUpdate = false;
        resolve();
      }
      catch (error) {
        console.error('检查更新失败:', error);
        isCheckingUpdate = false;
        resolve();
      }
    });
  });
}

/**
 * 显示更新确认对话框
 */
function showUpdateModal(
  config: Required<UpdateConfig>,
  versionInfo: VersionInfo,
  onConfirm: () => void,
): void {
  console.log('🚀 测试：不使用 confirmText 和 cancelText');

  uni.showModal({
    title: config.title,
    content: versionInfo.versionDesc || config.content,
    showCancel: !versionInfo.isForcedUpgrade,
    // 先不使用自定义按钮文本
    // confirmText: config.oktext,
    // cancelText: config.canceltext,
    success: (res) => {
      console.log('✅ 弹框回调:', res);
      if (res.confirm) {
        console.log('👍 用户确认升级');
        onConfirm();
      }
      else {
        console.log('❌ 用户取消了升级');
      }
    },
  });
}

/**
 * 创建下载进度条视图
 */
function createProgressViews(config: Required<UpdateConfig>): {
  backgroundView: any;
  progressView: any;
  width: number;
} {
  const width = Math.floor((plus.screen?.resolutionWidth || 750) / 2);
  const left = Math.floor(width / 2);

  const backgroundView = new (plus.nativeObj as any).View('maskView', {
    backgroundColor: config.barbackground,
    left: `${left}px`,
    bottom: '80px',
    width: `${width}px`,
    height: '10px',
  });

  const progressView = new (plus.nativeObj as any).View('maskViewinner', {
    backgroundColor: config.barbackgroundactive,
    left: `${left}px`,
    bottom: '80px',
    width: '1px',
    height: '10px',
  });

  backgroundView.show();
  progressView.show();

  return { backgroundView, progressView, width };
}

/**
 * 处理下载完成后的安装
 */
function handleInstallation(filename: string): void {
  plus.runtime.install(
    filename,
    { force: false },
    () => {
      // 安装成功，重启应用
      plus.runtime.restart();
    },
    (error) => {
      uni.showToast({
        title: `安装升级包失败: ${JSON.stringify(error)}`,
        icon: 'none',
      });
    },
  );
}

/**
 * 开始下载更新包
 */
function startDownload(config: Required<UpdateConfig>, versionInfo: VersionInfo): void {
  if (!versionInfo.downloadUrl) {
    return;
  }

  const downloadTask = plus.downloader.createDownload(
    versionInfo.downloadUrl,
    { filename: '_downloads/' },
    (downloadResult, status) => {
      console.log('下载结果:', downloadResult);

      if (status === 200 && downloadResult.filename) {
        handleInstallation(downloadResult.filename);
      }
      else {
        plus.nativeUI.alert(`下载升级包失败，请手动去站点下载安装，错误码: ${status}`);
      }
    },
  );

  // 创建进度条
  const { progressView, width } = createProgressViews(config);

  // 监听下载进度
  downloadTask.addEventListener(
    'statechanged',
    (event: any) => {
      if (event?.downloadedSize > 0 && event?.totalSize > 0) {
        const progress = Math.floor((event.downloadedSize / event.totalSize) * width);
        progressView.setStyle({ width: `${progress}px` });
      }
    },
    false,
  );

  downloadTask.start();
}

/**
 * 处理Android更新
 */
function handleAndroidUpdate(config: Required<UpdateConfig>, versionInfo: VersionInfo): void {
  showUpdateModal(config, versionInfo, () => {
    startDownload(config, versionInfo);
  });
}

/**
 * 检查微信小程序更新
 */
export function checkMpUpdate() {
  const updateManager = uni.getUpdateManager();
  updateManager.onCheckForUpdate((res) => {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate);
  });
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '检测到新版本，是否下载新版本并重启小程序？',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        }
      },
    });
  });
  updateManager.onUpdateFailed(() => {
    // 新的版本下载失败
    uni.showModal({
      title: '已经有新版本了哟~',
      content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
      showCancel: false,
    });
  });
}

export default {
  checkApp,
  checkMpUpdate,
};
